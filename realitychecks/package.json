{"name": "realitychecks", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/roboto": "^5.0.14", "@mui/icons-material": "^5.16.7", "@mui/material": "^5.16.7", "@pixi/core": "^7.4.3", "@pixi/display": "^7.4.3", "@pixi/utils": "^7.4.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.9.0", "pixi.js": "^6.5.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.26.0", "react-scripts": "5.0.1", "react-scroll-to-bottom": "^4.2.0", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "socket.io-client": "^4.8.1", "web-vitals": "^2.1.4", "websocket": "^1.0.35"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}