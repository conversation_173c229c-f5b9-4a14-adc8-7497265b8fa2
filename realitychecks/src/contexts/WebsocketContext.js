import React, { createContext, useContext, useEffect, useState } from 'react';
import WebSocketService from '../services/WebsocketService.js';

const WebSocketContext = createContext(null);
const DefaultReconnectInterval = 5000; // Default reconnect interval in milliseconds

export function WebSocketProvider({ children }) {
    const [isConnected, setIsConnected] = useState(false);
    const [lastMessage, setLastMessage] = useState(null);
    const [reconnectInterval, setReconnectInterval] = useState(3000);

    useEffect(() => {
        // Connect when the provider mounts
        WebSocketService.connect();

        //Reconnect logic
        const reconnect = () => {
            clearInterval(reconnectInterval);
            setReconnectInterval(setInterval(() => {
                if (!WebSocketService.socket || WebSocketService.socket.readyState === WebSocket.CLOSED) {
                    console.log('Reconnecting...');
                    WebSocketService.connect();
                }
            }, reconnectInterval || DefaultReconnectInterval)); // Default 3 seconds
        };

        // Set up event listeners
        const handleOpen = () => {
            setIsConnected(true);
        };

        const handleClose = () => {
            clearInterval(reconnectInterval);
            setReconnectInterval(DefaultReconnectInterval);
            setIsConnected(false);
            reconnect();
        };

        const handleMessage = (message) => {
            setLastMessage(message);
        };

        WebSocketService.socket.addEventListener('open', handleOpen);
        WebSocketService.socket.addEventListener('close', handleClose);
        WebSocketService.socket.addEventListener('message', handleMessage);

        // Clean up when the provider unmounts
        return () => {
            WebSocketService.socket.removeEventListener('open', handleOpen);
            WebSocketService.socket.removeEventListener('close', handleClose);
            WebSocketService.socket.removeEventListener('message', handleMessage);
            // WebSocketService.disconnect();
        };
    }, [reconnectInterval]);

    // Create a set of helper functions for components to use
    const sendMessage = (type, payload) => {
        WebSocketService.send(type, payload);
    };

    const subscribe = (event, callback) => {
        WebSocketService.on(event, callback);
        // Return an unsubscribe function
        return () => WebSocketService.off(event, callback);
    };

    const contextValue = {
        isConnected,
        lastMessage,
        sendMessage,
        subscribe,
    };

    return (
        <WebSocketContext.Provider value={contextValue}>
            {children}
        </WebSocketContext.Provider>
    );
}

// Custom hook for using the WebSocket context
export function useWebSocket() {
    const context = useContext(WebSocketContext);
    if (!context) {
        throw new Error('useWebSocket must be used within a WebSocketProvider');
    }
    return context;
}