import React, { createContext, useContext, useState, useEffect } from 'react';

const ChatContext = createContext();

export const ChatProvider = ({ children }) => {
    // Initialize from localStorage or default
    const [chatSessions, setChatSessions] = useState(() => {
        const saved = localStorage.getItem('chatSessions');
        return saved ? JSON.parse(saved) : {
            activeChatId: null,
            chats: {}, // e.g., { chat1: [{ id, text, sender }], chat2: [...] }
        };
    });

    // Save to localStorage on change
    useEffect(() => {
        localStorage.setItem('chatSessions', JSON.stringify(chatSessions));
    }, [chatSessions]);

    const setActiveChat = (chatId) => {
        setChatSessions((prev) => ({ ...prev, activeChatId: chatId }));
    };

    const addMessage = (chatId, message) => {
        setChatSessions((prev) => ({
            ...prev,
            chats: {
                ...prev.chats,
                [chatId]: [...(prev.chats[chatId] || []), message],
            },
        }));
    };

    return (
        <ChatContext.Provider value={{ chatSessions, setActiveChat, addMessage }}>
            {children}
        </ChatContext.Provider>
    );
};

export const useChat = () => useContext(ChatContext);