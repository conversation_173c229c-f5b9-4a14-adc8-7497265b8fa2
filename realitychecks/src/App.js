import React, { useMemo } from "react";
import "./App.css";
import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import MainPlatform from "./pages/MainPlatform";
import Features from "./pages/Features";
import MainLayout from "./components/MainLayout";
import { WebSocketProvider } from "./contexts/WebsocketContext";
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { ChatProvider } from './contexts/ChatContext';

const KickOffSocket = () => {
  const bodyContent = {
    serial: "00000000000000",
    timestamp: Date.now(),
    dateTime: "",
    member: {
      id: Math.floor(Math.random() * (100 - 5 + 1)) + 5,
      background: "",
      icon: "",
      identity: "",
      username: "",
    },
    type: "text",
    text: "Test Text Number One",
    image: {},
    voice: {},
    video: {},
    pointers: [],
  };

  const messageContent = {
    dataItem: "message",
    fromIdentity: "gee",
    toIdentity: "bee",
    fromUsername: "floatingGee",
    messageType: "send",
    body: JSON.stringify(bodyContent),
    timestamp: Date.now(),
  };

  const message = {
    type: "Communication",
    payload: JSON.stringify(messageContent),
  };
};

const App = () => {
  const theme = useMemo(
    () =>
      createTheme({
        palette: {
          mode: 'dark',
          background: {
            default: '#121212',
            paper: '#1e1e1e',
          },
          text: {
            primary: '#ffffff',
            secondary: '#b0bec5',
          },
        },
      }),
    []
  );

  KickOffSocket();
  return (
    <WebSocketProvider>
      <ThemeProvider theme={theme}>
      <ChatProvider>
      <Router>
        <Routes>
          <Route path="/" element={<MainLayout />}>
            <Route path="/ai" element={<MainPlatform />} />
            <Route path="/features" element={<Features />} />
          </Route>
        </Routes>
      </Router>
      </ChatProvider>
      </ThemeProvider>
    </WebSocketProvider>
  );
};

export default App;
