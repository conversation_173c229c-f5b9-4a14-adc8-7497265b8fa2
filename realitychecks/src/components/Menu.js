// src/components/Menu.js
import React from 'react';
import { Link } from 'react-router-dom';
import './Menu.css'; // For styling
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import ListItemIcon from '@mui/material/ListItemIcon';
import Typography from '@mui/material/Typography';
import DeveloperBoardIcon from '@mui/icons-material/DeveloperBoard';
import DeblurIcon from '@mui/icons-material/Deblur';

const MainMenu = () => {
    return (
        <Box
            sx={{
                p: 1,
                borderRadius: 2,
                bgcolor: 'dark',
                '&:hover': {
                    bgcolor: 'dark',
                },
                display: 'grid',
                gridTemplateColumns: { md: '1fr' },
                gap: 0,
                height: '100%',
                
            }}
        >
            <Paper elevation={4}>
                <MenuList>
                    <MenuItem component={Link} to="/ai">
                        <ListItemIcon>
                            <DeveloperBoardIcon fontSize="small" />
                        </ListItemIcon>
                        <Typography variant="inherit">AI Convos</Typography>
                    </MenuItem>
                    <MenuItem component={Link} to="/features">
                        <ListItemIcon>
                            <DeblurIcon fontSize="small" />
                        </ListItemIcon>
                        <Typography variant="inherit">AI Posts</Typography>
                    </MenuItem>
                </MenuList>
            </Paper>
        </Box>
    );
};

export default MainMenu;