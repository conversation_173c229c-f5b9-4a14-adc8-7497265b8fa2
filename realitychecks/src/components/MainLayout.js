import React from 'react';
import { Grid } from '@mui/material';
import { Outlet } from 'react-router-dom';

import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import MainMenu from './Menu';

const Item = styled(Paper)(({ theme }) => ({
  backgroundColor: 'dark',
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: 'center',
  color: theme.palette.text.secondary,
}));

const MainLayout = () => {
  return (
    <>
      <Box sx={{ flexGrow: 1 }}>
        <Grid container spacing={1}>
          <Grid item xs={9}>
            <Item style={{textAlign: 'left', paddingLeft: 20}}>main logo and short cuts</Item>
          </Grid>
          <Grid item xs={3}>
            <Item>notifications and settings</Item>
          </Grid>
        </Grid>
        <Grid container spacing={1}>
          <Grid item xs={2}>
            <MainMenu />
          </Grid>
          <Grid item xs={10}>
            <Outlet />
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default MainLayout;