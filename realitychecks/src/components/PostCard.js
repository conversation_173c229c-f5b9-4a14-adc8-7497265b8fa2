import { useState } from "react";
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Modal from "@mui/material/Modal";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

export default function OutlinedCard(props) {
    const mainData = props.props;
    const [modalOpen, setModalOpen] = useState(false);

    const handleOpenModal = () => setModalOpen(true);
    const handleCloseModal = () => setModalOpen(false);

    // Function to truncate text
    const truncateText = (text, maxLength = 200) => {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    };

    return (
        <>
            <Box sx={{ width: '100%', maxWidth: { xs: 350, sm: 400, md: 350 }, mx: 'auto' }}>
                <Card
                    variant="outlined"
                    sx={{
                        width: '100%',
                        height: 280, // Fixed height for all cards
                        display: 'flex',
                        flexDirection: 'column',
                        overflow: 'hidden',
                        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                        '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: 4
                        }
                    }}
                >
                    <CardContent sx={{
                        flexGrow: 1,
                        overflow: 'hidden',
                        display: 'flex',
                        flexDirection: 'column',
                        p: 2
                    }}>
                        <Typography
                            gutterBottom
                            sx={{
                                color: "text.secondary",
                                fontSize: 12,
                                fontWeight: 500
                            }}
                        >
                            {mainData.title}
                        </Typography>
                        <Typography
                            variant="h6"
                            component="div"
                            sx={{
                                mb: 1,
                                fontSize: '1rem',
                                lineHeight: 1.3
                            }}
                        >
                            {mainData.tag} - {mainData.category}
                        </Typography>
                        <Typography
                            sx={{
                                color: "text.secondary",
                                mb: 1.5,
                                fontSize: '0.85rem',
                                lineHeight: 1.4
                            }}
                        >
                            {mainData.subtitle}
                        </Typography>
                        <Box sx={{
                            flexGrow: 1,
                            overflow: 'hidden',
                            mb: 2
                        }}>
                            <Typography
                                variant="body2"
                                sx={{
                                    fontSize: '0.8rem',
                                    lineHeight: 1.5,
                                    display: '-webkit-box',
                                    WebkitLineClamp: 6, // Limit to 6 lines
                                    WebkitBoxOrient: 'vertical',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis'
                                }}
                            >
                                {truncateText(mainData.content, 300)}
                            </Typography>
                        </Box>
                        <Typography
                            sx={{
                                color: "text.secondary",
                                fontSize: '0.75rem',
                                mt: 'auto',
                                textAlign: 'right'
                            }}
                        >
                            {mainData.time}
                        </Typography>
                    </CardContent>
                    <CardActions sx={{
                        mt: 'auto',
                        justifyContent: 'center',
                        p: 1
                    }}>
                        <Button
                            size="small"
                            variant="outlined"
                            onClick={handleOpenModal}
                            sx={{
                                fontSize: '0.75rem',
                                px: 2
                            }}
                        >
                            LIVE READER
                        </Button>
                    </CardActions>
                </Card>
            </Box>

            {/* Modal for full content */}
            <Modal
                open={modalOpen}
                onClose={handleCloseModal}
                aria-labelledby="modal-title"
                aria-describedby="modal-description"
            >
                <Box sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: { xs: '90%', sm: '80%', md: '70%', lg: '60%' },
                    maxWidth: '800px',
                    maxHeight: '90vh',
                    bgcolor: 'background.paper',
                    border: '2px solid #000',
                    boxShadow: 24,
                    p: 4,
                    overflow: 'auto'
                }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography id="modal-title" variant="h6" component="h2">
                            {mainData.title}
                        </Typography>
                        <IconButton onClick={handleCloseModal}>
                            <CloseIcon />
                        </IconButton>
                    </Box>

                    <Typography variant="h5" sx={{ mb: 2, color: 'primary.main' }}>
                        {mainData.tag} - {mainData.category}
                    </Typography>

                    <Typography variant="subtitle1" sx={{ mb: 3, color: 'text.secondary' }}>
                        {mainData.subtitle}
                    </Typography>

                    <Box sx={{ mb: 3 }}>
                        <ReactMarkdown
                            rehypePlugins={[rehypeRaw]}
                            components={{
                                code({ node, inline, className, children, ...props }) {
                                    const match = /language-(\w+)/.exec(className || '');
                                    return !inline && match ? (
                                        <SyntaxHighlighter
                                            style={vscDarkPlus}
                                            language={match[1]}
                                            PreTag="div"
                                            customStyle={{
                                                fontSize: '0.9rem',
                                                margin: '16px 0'
                                            }}
                                            {...props}
                                        >
                                            {String(children).replace(/\n$/, '')}
                                        </SyntaxHighlighter>
                                    ) : (
                                        <code className={className} {...props}>
                                            {children}
                                        </code>
                                    );
                                },
                                p: ({ children }) => (
                                    <Typography variant="body1" sx={{ mb: 2, lineHeight: 1.6 }}>
                                        {children}
                                    </Typography>
                                )
                            }}
                        >
                            {mainData.content}
                        </ReactMarkdown>
                    </Box>

                    <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                        Created: {mainData.time}
                    </Typography>
                </Box>
            </Modal>
        </>
    );
}
