import * as React from "react";
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

export default function OutlinedCard(props) {
    const mainData = props.props;
    return (
        <Box sx={{ minWidth: 350 }}>
            <Card variant="outlined" sx={{ maxWidth: 350, minHeight: 600}}>
                <React.Fragment>
                    <CardContent>
                        <Typography gutterBottom sx={{ color: "text.secondary", fontSize: 14 }}>
                            {mainData.title}
                        </Typography>
                        <Typography variant="h5" component="div">
                            {mainData.tag} - {mainData.category}
                        </Typography>
                        <Typography sx={{ color: "text.secondary", mb: 1.5 }}>
                            {mainData.subtitle}
                        </Typography>
                        <ReactMarkdown
                            rehypePlugins={[rehypeRaw]}
                            components={{
                            code({ node, inline, className, children, ...props }) {
                                const match = /language-(\w+)/.exec(className || '');
                                return !inline && match ? (
                                <SyntaxHighlighter
                                    style={vscDarkPlus}
                                    language={match[1]}
                                    PreTag="div"
                                    {...props}
                                >
                                    {String(children).replace(/\n$/, '')}
                                </SyntaxHighlighter>
                                ) : (
                                <code className={className} {...props}>
                                    {children}
                                </code>
                                );
                            },
                            }}
                        >
                            {mainData.content}
                        </ReactMarkdown>
                        {/* <Typography variant="body2">
                            {mainData.content}
                        </Typography> */}
                        <Typography sx={{ color: "text.secondary", mb: 1.5 }}>
                            {mainData.time}
                        </Typography>
                    </CardContent>
                    <CardActions>
                        <Button size="small">LIVE READER</Button>
                    </CardActions>
                </React.Fragment>
            </Card>
        </Box>
    );
}
