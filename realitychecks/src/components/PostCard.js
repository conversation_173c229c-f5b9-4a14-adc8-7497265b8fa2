import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

export default function OutlinedCard(props) {
    const mainData = props.props;
    return (
        <Box sx={{ width: '100%', maxWidth: { xs: 350, sm: 400, md: 350 }, mx: 'auto' }}>
            <Card
                variant="outlined"
                sx={{
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    minHeight: { xs: 350, sm: 400, md: 450 },
                    maxHeight: { xs: 500, sm: 550, md: 600 },
                    overflow: 'hidden',
                    transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                    '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: 4
                    }
                }}
            >
                <CardContent sx={{
                    flexGrow: 1,
                    overflow: 'hidden',
                    display: 'flex',
                    flexDirection: 'column',
                    p: { xs: 2, sm: 2.5, md: 3 }
                }}>
                    <Typography
                        gutterBottom
                        sx={{
                            color: "text.secondary",
                            fontSize: { xs: 12, sm: 13, md: 14 },
                            fontWeight: 500
                        }}
                    >
                        {mainData.title}
                    </Typography>
                    <Typography
                        variant="h6"
                        component="div"
                        sx={{
                            mb: 1,
                            fontSize: { xs: '1rem', sm: '1.1rem', md: '1.25rem' },
                            lineHeight: 1.3
                        }}
                    >
                        {mainData.tag} - {mainData.category}
                    </Typography>
                    <Typography
                        sx={{
                            color: "text.secondary",
                            mb: 1.5,
                            fontSize: { xs: '0.8rem', sm: '0.85rem', md: '0.9rem' },
                            lineHeight: 1.4
                        }}
                    >
                        {mainData.subtitle}
                    </Typography>
                    <Box sx={{
                        flexGrow: 1,
                        overflow: 'auto',
                        mb: 2,
                        '&::-webkit-scrollbar': {
                            width: '4px',
                        },
                        '&::-webkit-scrollbar-track': {
                            background: 'transparent',
                        },
                        '&::-webkit-scrollbar-thumb': {
                            background: 'rgba(0,0,0,0.2)',
                            borderRadius: '2px',
                        },
                    }}>
                        <ReactMarkdown
                            rehypePlugins={[rehypeRaw]}
                            components={{
                                code({ node, inline, className, children, ...props }) {
                                    const match = /language-(\w+)/.exec(className || '');
                                    return !inline && match ? (
                                        <SyntaxHighlighter
                                            style={vscDarkPlus}
                                            language={match[1]}
                                            PreTag="div"
                                            customStyle={{
                                                fontSize: '0.75rem',
                                                maxHeight: '150px',
                                                overflow: 'auto',
                                                margin: '8px 0'
                                            }}
                                            {...props}
                                        >
                                            {String(children).replace(/\n$/, '')}
                                        </SyntaxHighlighter>
                                    ) : (
                                        <code className={className} {...props}>
                                            {children}
                                        </code>
                                    );
                                },
                                p: ({ children }) => (
                                    <Typography
                                        variant="body2"
                                        sx={{
                                            mb: 1,
                                            fontSize: { xs: '0.8rem', sm: '0.85rem', md: '0.9rem' },
                                            lineHeight: 1.5
                                        }}
                                    >
                                        {children}
                                    </Typography>
                                )
                            }}
                        >
                            {mainData.content}
                        </ReactMarkdown>
                    </Box>
                    <Typography
                        sx={{
                            color: "text.secondary",
                            fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' },
                            mt: 'auto',
                            textAlign: 'right'
                        }}
                    >
                        {mainData.time}
                    </Typography>
                </CardContent>
                <CardActions sx={{
                    mt: 'auto',
                    justifyContent: 'center',
                    p: { xs: 1, sm: 1.5, md: 2 }
                }}>
                    <Button
                        size="small"
                        variant="outlined"
                        sx={{
                            fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' },
                            px: { xs: 2, sm: 3 }
                        }}
                    >
                        LIVE READER
                    </Button>
                </CardActions>
            </Card>
        </Box>
    );
}
