import React, { useState, useEffect, useRef } from "react";
import {
    <PERSON>,
    Typography,
    TextField,
    IconButton,
    Avatar,
    Divider,
    List,
    ListItem,
    ListItemAvatar,
    ListItemText,
    AppBar,
    Toolbar,
    Badge,
    Paper,
    InputAdornment,
    Tooltip,
    CircularProgress,
    Chip,
    Menu,
    MenuItem,
    Tabs,
    Tab,
    Grid,
} from "@mui/material";
import {
    Send as SendIcon,
    Menu as MenuIcon,
    MoreVert as MoreVertIcon,
    AttachFile as AttachFileIcon,
    EmojiEmotions as EmojiIcon,
    Search as SearchIcon,
    Notifications as NotificationsIcon,
    Person as PersonIcon,
    Castle as CastleIcon,
    ExitToApp as LogoutIcon,
    Settings as SettingsIcon,
    LaptopRounded as LaptopRoundedIcon,
} from "@mui/icons-material";
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

import { css } from "@emotion/css";
import ScrollToBottom from "react-scroll-to-bottom";
import { useWebSocket } from "../contexts/WebsocketContext.js";
import { useChat } from '../contexts/ChatContext';

// Sample user data
const CURRENT_USER = {
    id: 1,
    name: "BigPieceOfShit",
    avatar: "../assets/avatars/bowie.png",
    status: "online",
};

const CONTACTS = [
    {
        id: 2,
        name: "AI Chatbots 1",
        avatar: "/assets/avatars/beyonce.png",
        status: "online",
        unread: 0,
    },
    {
        id: 3,
        name: "AI Chatbots 2",
        avatar: "/assets/avatars/brain.png",
        status: "offline",
        unread: 0,
    },
    {
        id: 4,
        name: "AI Chatbots 3",
        avatar: "/assets/avatars/doll.png",
        status: "online",
        unread: 0,
    },
    {
        id: 5,
        name: "AI Chatbots 4",
        avatar: "/assets/avatars/helm.png",
        status: "away",
        unread: 0,
    },
];

const _messageConsolidation = (messageEntityText) => {

    // message consolidation
    /**
     * type ModelResponse struct {
            SessionId      string `json:"sessionId"`
            TimeOfResponse int64  `json:"timeOfResponse"`
            NameOfModel    string `json:"nameOfModel"`
            ModelInfo      string `json:"modelInfo"`
            Content        string `json:"content"`
        }
     */
    const messageEntity = JSON.parse(messageEntityText);
    const timestamp = new Date().getTime();
    let messageContent = '';
    if (messageEntity.content) {
        messageContent = messageEntity.content.replace(/<think>.*?<\/think>/g, '').replace(/$$ .*? $$/g, '').trim();
        messageContent = messageContent.replace('\\n', '<br />');
    }

    return {
        id: messageEntity.sessionId + timestamp,
        senderId: `${messageEntity.nameOfModel}-${messageEntity.modelInfo}`,
        text: `${messageContent}`,
        timestamp: messageEntity.timeOfResponse,
    }
}

const ChatApp = () => {
    const [drawerOpen, setDrawerOpen] = useState(true);
    const [selectedContact, setSelectedContact] = useState(null);
    const [searchQuery, setSearchQuery] = useState("");
    const [anchorEl, setAnchorEl] = useState(null);
    const [tabValue, setTabValue] = useState(0);
    const [loading, setLoading] = useState(false);
    const [messages, setMessages] = useState([]);
    const [inputValue, setInputValue] = useState("");
    const [sessionIds, setSessionIds] = useState([]);
    const { isConnected, sendMessage, subscribe } = useWebSocket();
    const { chatSessions, setActiveChat, addMessage } = useChat();
    // const [chatId, setChatId] = useState(chatSessions.activeChatId || `qwen3:4b_gemma3:4b_${CURRENT_USER.id}`);
    const [chatId, setChatId] = useState(`qwen3:4b_gemma3:4b_${CURRENT_USER.id}`);

    const messageInputRef = useRef(null);

    // Filter contacts based on search query
    const filteredContacts = CONTACTS.filter((contact) => contact.name.toLowerCase().includes(searchQuery.toLowerCase()));

    const _wrapPayload = (payload) => JSON.stringify(payload);

    useEffect(() => {

        //chatSessions?.chats[chatId]?.forEach(message => setMessages(prevMessages => [...prevMessages, JSON.parse(message.text)]));

        // Subscribe to chat messages
        const subscribeMessage = subscribe('AIChatSession', (message) => {
            setLoading(true);

            console.log("subscribeMessage");

            if (message?.startsWith("#AIChatSession-")) {
                console.log("AIChatSession message received");
                console.log(message);
                setSessionIds(sessionIds);
                setLoading(false);
                return;
            }

            let messagesNew = _messageConsolidation(message);
            console.log(messagesNew);
            setMessages(prevMessages => [...prevMessages, messagesNew]);
            // addMessage(chatId, { id: messages.length + 1, text: JSON.stringify(messagesNew), sender: CURRENT_USER.id });
            setLoading(false);
        });
        
        // Subscribe to user joined notifications
        const subscribeUserJoined = subscribe('user_joined', (user) => {
            setLoading(true);
            setMessages(prevMessages => [
                ...prevMessages,
                { type: 'notification', content: `${user.name} joined the chat` }
            ]);
            setLoading(false);
        });

        // Clean up subscriptions
        return () => {
            subscribeMessage();
            subscribeUserJoined();
        };
    }, [addMessage, chatId, chatSessions?.chats, inputValue, messages.length, sendMessage, sessionIds, subscribe]);

    const handleSendMessage = (e) => {
        console.log("---=====---=====");
        console.log(e.target);
        console.log(isConnected);
        console.log(inputValue);
        e.preventDefault();

        if (!(inputValue.trim() && isConnected)) return;
        const newMsg = {
            id: messages.length + 1,
            senderId: CURRENT_USER.id,
            text: inputValue.trim(),
            timestamp: new Date(),
        };

        // const chatId = `qwen3:4b_gemma3:4b_${CURRENT_USER.id}`;
        //addMessage(chatId, { id: messages.length + 1, text: JSON.stringify(newMsg), sender: CURRENT_USER.id });

        const messageType = 'AIChatSession';
        sendMessage(messageType, _wrapPayload({
            initiationPrompt: inputValue.trim(),
            SessionLength: 50,
            aiModels: ["qwen3:4b", "gemma3:4b"],
            queryType: "init",
            queryContent: "",
        }));

        setMessages([...messages, newMsg]);
        setInputValue('');
    };

    // Format timestamp
    const formatTime = (date) => {
        return new Date(date).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
        });
    };

    const SCROLL_CSS = css({
        height: "calc(100vh - 300px)",
        width: "100%",
        overflow: "auto",
        padding: "16px",

        // Customize scrollbar
        "&::-webkit-scrollbar": {
            width: "8px",
        },
        "&::-webkit-scrollbar-track": {
            backgroundColor: "#f1f1f1",
        },
        "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#c1c1c1",
            borderRadius: "4px",
            "&:hover": {
                backgroundColor: "#a8a8a8",
            },
        },
    });
    return (
        <>
            <Box sx={{ flexGrow: 1 }}>
                <Grid container spacing={1} style={{ marginBottom: 10, marginTop: 0 }}>
                    <Grid item xs={1}>
                    <Typography variant="body2" color="textSecondary">
                        <LaptopRoundedIcon
                            sx={{ fontSize: 60, verticalAlign: "middle", textAlign: "center", mr: 0.5 }}
                        />
                    </Typography>
                    </Grid>
                    <Grid item xs={11}>
                        {/* Main App Bar */}
                        <AppBar position="static" style={{ bgColor: 'primary', borderRadius: 10 }}>
                            <Toolbar>
                                <IconButton
                                    color="inherit"
                                    edge="start"
                                    onClick={() => setDrawerOpen(!drawerOpen)}
                                    sx={{ mr: 2, display: { sm: "none" } }}
                                >
                                    <MenuIcon />
                                </IconButton>

                                <Box sx={{ display: "flex", alignItems: "center" }}>
                                    <Avatar
                                        src={CURRENT_USER.avatar}
                                        sx={{ width: 40, height: 40, mr: 2 }}
                                    />
                                    <Typography variant="h6" noWrap>
                                        AI Convos
                                    </Typography>
                                </Box>

                                <Box sx={{ flexGrow: 1 }} />

                                <Box sx={{ display: "flex" }}>
                                    <IconButton color="inherit">
                                        <Badge badgeContent={4} color="error">
                                            <NotificationsIcon />
                                        </Badge>
                                    </IconButton>
                                    <IconButton
                                        color="inherit"
                                        onClick={(e) => setAnchorEl(e.currentTarget)}
                                    >
                                        <MoreVertIcon />
                                    </IconButton>

                                    <Menu
                                        anchorEl={anchorEl}
                                        open={Boolean(anchorEl)}
                                        onClose={() => setAnchorEl(null)}
                                    >
                                        <MenuItem onClick={() => setAnchorEl(null)}>
                                            <PersonIcon fontSize="small" sx={{ mr: 1 }} />
                                            Profile
                                        </MenuItem>
                                        <MenuItem onClick={() => setAnchorEl(null)}>
                                            <SettingsIcon fontSize="small" sx={{ mr: 1 }} />
                                            Settings
                                        </MenuItem>
                                        <Divider />
                                        <MenuItem onClick={() => setAnchorEl(null)}>
                                            <LogoutIcon fontSize="small" sx={{ mr: 1 }} />
                                            Logout
                                        </MenuItem>
                                    </Menu>
                                </Box>
                            </Toolbar>
                        </AppBar>
                    </Grid>
                </Grid>
                <Grid container spacing={1}>
                    <Grid item xs={3}>
                        {/* Contact Drawer */}
                        <Paper elevation={4}>
                            <Box sx={{ p: 2 }}>
                                <TextField
                                    fullWidth
                                    placeholder="Search contacts..."
                                    variant="outlined"
                                    size="small"
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <SearchIcon fontSize="small" />
                                            </InputAdornment>
                                        ),
                                    }}
                                />
                            </Box>

                            <Tabs
                                value={tabValue}
                                onChange={(e, newValue) => setTabValue(newValue)}
                                sx={{ borderBottom: 1, borderColor: "divider" }}
                                centered
                            >
                                <Tab label="Chats" />
                                <Tab label="Contacts" />
                            </Tabs>

                            <List sx={{ p: 0 }}>
                                {filteredContacts.map((contact) => (
                                    <ListItem
                                        key={contact.id}
                                        button
                                        selected={selectedContact?.id === contact.id}
                                        onClick={() => setSelectedContact(contact)}
                                        sx={{
                                            borderBottom: "1px solid rgba(0, 0, 0, 0.06)",
                                            "&.Mui-selected": {
                                                backgroundColor: "rgba(25, 118, 210, 0.08)",
                                            },
                                        }}
                                    >
                                        <ListItemAvatar>
                                            <Badge
                                                overlap="circular"
                                                anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
                                                variant="dot"
                                                color={
                                                    contact.status === "online"
                                                        ? "success"
                                                        : contact.status === "away"
                                                            ? "warning"
                                                            : "error"
                                                }
                                            >
                                                <Avatar src={contact.avatar} />
                                            </Badge>
                                        </ListItemAvatar>
                                        <ListItemText
                                            primary={contact.name}
                                            secondary={contact.status}
                                            primaryTypographyProps={{
                                                fontWeight: contact.unread > 0 ? "bold" : "regular",
                                            }}
                                        />
                                        {contact.unread > 0 && (
                                            <Chip
                                                label={contact.unread}
                                                color="primary"
                                                size="small"
                                                sx={{ height: 24, minWidth: 24 }}
                                            />
                                        )}
                                    </ListItem>
                                ))}
                            </List>
                            <Box sx={{ p: 2, display: "flex", justifyContent: "center" }}>
                                <Typography variant="body2" color="textSecondary">
                                    <LaptopRoundedIcon
                                        sx={{ fontSize: 16, verticalAlign: "middle", mr: 0.5 }}
                                    />
                                    AI CONVOS v1.0.0
                                </Typography>
                            </Box>
                        </Paper>
                    </Grid>
                    <Grid item xs={9} style={{ position: "relative", overflow: "hidden" }}>
                        {/* Main Chat Area */}
                        <Box
                            position="static"
                            component="main"
                            sx={{
                                flexGrow: 1,
                                p: 0,
                                width: { sm: `100%` },
                                height: "100%",
                                display: "flex",
                                flexDirection: "column",
                            }}
                        >
                            {selectedContact ? (
                                <>
                                    {/* Chat Header */}
                                    <Box
                                        sx={{
                                            p: 2,
                                            display: "flex",
                                            alignItems: "center",
                                            borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
                                            backgroundColor: "background.paper",
                                        }}
                                    >
                                        <Avatar src={selectedContact.avatar} sx={{ mr: 2 }} />
                                        <Box>
                                            <Typography variant="h6">{selectedContact.name}</Typography>
                                            <Typography variant="body2" color="textSecondary">
                                                {selectedContact.status === "online"
                                                    ? "Online"
                                                    : selectedContact.status === "away"
                                                        ? "Away"
                                                        : "Offline"}
                                            </Typography>
                                        </Box>
                                    </Box>

                                    {/* Messages Area */}
                                    {loading ? (
                                        <Box
                                            sx={{
                                                display: "flex",
                                                justifyContent: "center",
                                                alignItems: "center",
                                                height: "calc(100vh - 200px)",
                                                width: "100%",
                                            }}
                                        >
                                            <CircularProgress />
                                        </Box>
                                    ) : (
                                        <ScrollToBottom className={SCROLL_CSS}>
                                            {messages.map((message) => {
                                                const isCurrentUser = message.senderId === CURRENT_USER.id;
                                                return (
                                                    <Box
                                                        key={message.id}
                                                        sx={{
                                                            display: "flex",
                                                            justifyContent: isCurrentUser
                                                                ? "flex-end"
                                                                : "flex-start",
                                                            mb: 2,
                                                        }}
                                                    >
                                                        {!isCurrentUser && (
                                                            <Avatar
                                                                src={selectedContact.avatar}
                                                                sx={{ width: 36, height: 36, mr: 1, mt: 0.5 }}
                                                            />
                                                        )}
                                                        <Box
                                                            sx={{
                                                                maxWidth: "80%",
                                                                p: 2,
                                                                borderRadius: 2,
                                                                backgroundColor: isCurrentUser
                                                                    ? "primary.main"
                                                                    : "grey.100",
                                                                color: isCurrentUser ? "white" : "text.primary",
                                                                boxShadow: 1,
                                                            }}
                                                        >
                                                            {/* <Typography variant="body1" elevation={3} sx={{ mt: 2, p: 2, borderRadius: 1, backgroundColor: '#f5f5f5' }}>
                                                                {message.text}
                                                            </Typography> */}
                                                            <ReactMarkdown
                                                                rehypePlugins={[rehypeRaw]}
                                                                components={{
                                                                code({ node, inline, className, children, ...props }) {
                                                                    const match = /language-(\w+)/.exec(className || '');
                                                                    return !inline && match ? (
                                                                    <SyntaxHighlighter
                                                                        style={vscDarkPlus}
                                                                        language={match[1]}
                                                                        PreTag="div"
                                                                        {...props}
                                                                    >
                                                                        {String(children).replace(/\n$/, '')}
                                                                    </SyntaxHighlighter>
                                                                    ) : (
                                                                    <code className={className} {...props}>
                                                                        {children}
                                                                    </code>
                                                                    );
                                                                },
                                                                }}
                                                            >
                                                                {message.text}
                                                            </ReactMarkdown>
                                                            <Typography
                                                                variant="caption"
                                                                sx={{
                                                                    display: "block",
                                                                    textAlign: "right",
                                                                    mt: 0.5,
                                                                    color: isCurrentUser
                                                                        ? "rgba(255, 255, 255, 0.7)"
                                                                        : "text.secondary",
                                                                }}
                                                            >
                                                                {formatTime(message.timestamp)}
                                                            </Typography>
                                                        </Box>
                                                        {isCurrentUser && (
                                                            <Avatar
                                                                src={CURRENT_USER.avatar}
                                                                sx={{ width: 36, height: 36, ml: 1, mt: 0.5 }}
                                                            />
                                                        )}
                                                    </Box>
                                                );
                                            })}
                                        </ScrollToBottom>
                                    )}

                                    {/* Message Input */}
                                    <Box
                                        component="form"
                                        onSubmit={handleSendMessage}
                                        sx={{
                                            p: 2,
                                            backgroundColor: "background.paper",
                                            borderTop: "1px solid rgba(0, 0, 0, 0.12)",
                                            display: "flex",
                                            alignItems: "center",
                                        }}
                                    >
                                        <Tooltip title="Attach file">
                                            <IconButton size="large">
                                                <AttachFileIcon />
                                            </IconButton>
                                        </Tooltip>
                                        <TextField
                                            fullWidth
                                            placeholder="Type a message..."
                                            variant="outlined"
                                            size="small"
                                            value={inputValue}
                                            onChange={(e) => setInputValue(e.target.value)}
                                            inputRef={messageInputRef}
                                            sx={{ mx: 1 }}
                                            InputProps={{
                                                endAdornment: (
                                                    <InputAdornment position="end">
                                                        <IconButton size="small">
                                                            <EmojiIcon />
                                                        </IconButton>
                                                    </InputAdornment>
                                                ),
                                            }}
                                        />
                                        <IconButton
                                            color="primary"
                                            type="submit"
                                            disabled={!inputValue.trim()}
                                            size="large"
                                        >
                                            <SendIcon />
                                        </IconButton>
                                    </Box>
                                </>
                            ) : (
                                // Welcome screen when no contact is selected
                                <Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        height: "100%",
                                        width: "100%",
                                        p: 3,
                                        bgcolor: "background.default",
                                    }}
                                >
                                    <Avatar
                                        sx={{
                                            width: 200,
                                            height: 200,
                                            bgcolor: "primary",
                                            mb: 3,
                                        }}
                                    >
                                        <CastleIcon sx={{ fontSize: 160 }} />
                                    </Avatar>
                                    <Typography variant="h4" gutterBottom>
                                        AI Convos
                                    </Typography>
                                    <Typography
                                        variant="body1"
                                        color="textSecondary"
                                        sx={{ mb: 2, textAlign: "center" }}
                                    >
                                        where we watch AIs chatting
                                    </Typography>
                                    <Chip
                                        label="Version 1.0.0"
                                        color="primary"
                                        variant="outlined"
                                    />
                                </Box>
                            )}
                        </Box>
                    </Grid>
                </Grid>
            </Box>
        </>
    );
};

export default ChatApp;
