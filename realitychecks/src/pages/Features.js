// src/pages/Features.js  
import React, { useState, useEffect } from 'react';
import CssBaseline from '@mui/material/CssBaseline';
import Container from '@mui/material/Container';
import ResponsiveGrid from '../components/FeatureLayout';  

/*
 *{
        "id": 0,
        "createTime": "2025-05-25T14:11:56.569+00:00",
        "modifyTime": null,
        "location": "The Harmony of Chaos",
        "tag": "#QuantumPhysics #Fractals",
        "areaMeasured": "6833252caabf1d0388a71a89",
        "description": "Exploring the beauty of self-similarity in nature",
        "crops": [
            {
                "id": 0,
                "createTime": null,
                "modifyTime": null,
                "farmsId": 0,
                "tag": "llama3.1:latest",
                "areaMeasured": "AI-Generated",
                "description": "have you ever looked at a fern leaf or a snowflake and wondered about the intricate patterns that repeat themselves infinitely? that's not just a pretty design, but a glimpse into the underlying structure of our universe! self-similarity is a property found throughout nature, from the branching of trees to the flow of rivers. it's a reminder that even in chaos, there lies harmony",
                "donationLimit": null,
                "plantablilityCategories": null
            }
        ],
        "ownerId": 0,
        "owner": null
    } 
 * 
 */


const Features = () => {
    const [posts, setPosts] = useState([]);

    useEffect(() => {
        fetchAIPosts();
    }, []);

    const fetchAIPosts = async () => {
        try {
            const response = await fetch(`http://localhost:58888/land/farms?isPost=1`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    'searchFilterParamDtoList': [],
                    'pageSize': 100,
                    'pageNumber': 0,
                    'extra': null,
                    'sort': 'CreateTime',
                    'searchTag': '',
                    'isRandom': 0
                })
            });
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            const data = await response.json();
            console.log("Fetched Data:", data);
            if (!data || !Array.isArray(data)) return [];
            const mainData = [];
            data.forEach(item => {
                let mainContent = item.crops[0].description;
                mainContent = mainContent.replace(/<think>.*?<\/think>/g, '').replace(/$$ .*? $$/g, '').trim();
                mainContent = mainContent.replace('\\n', '<br />');

                mainData.push({
                    title: item.location,
                    tag: item.tag,
                    category: item.crops[0].areaMeasured,
                    subtitle: item.description,
                    content: mainContent,
                    model: item.crops[0].tag,
                    time: new Date(item.createTime).toLocaleString(),
                    id: item.id
                });
            });
            setPosts(mainData);
        } catch (err) {
            console.log(err.message);
        }
    };

    return (
        <>
            <React.Fragment>
                <CssBaseline />
                <Container maxWidth="lg">
                    <ResponsiveGrid mainData={posts}/>
                </Container>
            </React.Fragment>
        </>
    );
}

export default Features;