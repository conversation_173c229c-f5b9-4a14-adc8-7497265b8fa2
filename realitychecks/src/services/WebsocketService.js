class WebSocketService {
    
    constructor() {
        this.socket = null;
        this.url = "ws://localhost:8196/aihmx";
        this.listeners = {};
        // this.reconnectInterval = 5000;
    }

    // reconnect() {
    //     clearInterval(this.reconnectInterval);
    //     this.reconnectInterval = setInterval(() => {
    //         if (!this.socket || this.socket.readyState === WebSocket.CLOSED) {
    //             console.log('Reconnecting...');
    //             this.connect();
    //         }
    //     }, this.reconnectInterval || 5000); // Default 3 seconds
    // };

    connect() {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            console.log("WebSocket connection already established");
            return;
        }

        try {
            this.socket = new WebSocket(this.url);
        } catch (error) {
            console.log(error);
            this.socket = null;
            return;
        }

        this.socket.onopen = () => {
            console.log("WebSocket connection established");
            // Reconnect any registered event listeners
            Object.keys(this.listeners).forEach((event) => {
                this.listeners[event].forEach((callback) => {
                    this.on(event, callback);
                });
            });
        };

        this.socket.onmessage = (event) => {
            try {
                if (!event || !event.data) {
                    console.error("WebSocket message event is missing data");
                    return;
                }

                const data = JSON.parse(event.data);
                console.log("OnMessageData:::::" + event.data);
                const eventName = data.type;

                if (this.listeners[eventName]) {
                    this.listeners[eventName].forEach((callback) => {
                        callback(data.payload);
                    });
                }
            } catch (error) {
                console.error("(payload is not JSON) Error parsing WebSocket message:", error);
            }
        };

        this.socket.onerror = (error) => {
            console.error("WebSocket error:", error);
        };

        this.socket.onclose = (event) => {
            console.log("WebSocket connection closed:", event.code, event.reason);
            // Optionally implement reconnection logic here
            // this.reconnect();
        };
    }

    disconnect() {
        // clearInterval(this.reconnectInterval);
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
    }

    send(type, payload) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            const message = JSON.stringify({ type, payload });
            this.socket.send(message);
        } else {
            console.error("WebSocket is not connected");
        }
    }

    on(event, callback) {
        if (!this.listeners[event]) {
            this.listeners[event] = [];
        }
        this.listeners[event].push(callback);
    }

    off(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event] = this.listeners[event].filter(
                (cb) => cb !== callback
            );
        }
    }
}

// Create a singleton instance
const WebsocketService = new WebSocketService();
export default WebsocketService;
